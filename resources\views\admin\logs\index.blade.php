@extends('layouts.admin')

@section('title', 'Laravel Log Viewer')

{{-- Month options and related variables are now passed from the controller --}}

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
    </div>
</div>
@endsection

@section('content')
<!-- Logs DataTable -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- DataTable Container -->
    <div class="p-6">
        <!-- Custom Controls -->
        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                <label for="log-month" class="text-sm font-medium text-gray-700 whitespace-nowrap mb-1 sm:mb-0">Filter by Month:</label>
                <select id="log-month" class="material-input w-full min-w-[200px]">
                    @foreach($monthOptions as $opt)
                    <option value="{{ $opt['value'] }}" @if($opt['value']===$currentMonth) selected @endif>{{ $opt['label'] }}</option>
                    @endforeach
                </select>
                <button id="delete-month-log" class="whitespace-nowrap material-button material-button-sm material-button-danger flex items-center ml-0 sm:ml-2 w-full sm:w-auto mt-2 sm:mt-0">
                    <i class="material-icons text-sm mr-1">delete</i> Delete Month Log
                </button>
            </div>
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="material-icons text-gray-400 text-sm">search</i>
                    </div>
                    <input type="text" id="search-input" placeholder="Search logs..."
                           class="material-input pl-10 w-64">
                </div>
                <button id="refresh-table" class="material-button material-button-sm material-button-secondary p-2" title="Refresh">
                    <i class="material-icons text-sm">refresh</i>
                </button>
            </div>
        </div>

        <!-- Table -->
        <div>
            <table id="logs-table" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SR No</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTables will populate this -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Single Modal for all log entries -->
<div id="log-modal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0  bg-opacity-75 transition-opacity" onclick="hideLogModal()"></div>

        <!-- This element is to trick the browser into centering the modal contents. -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <!-- Header -->
            <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-blue-50">
                <h2 class="text-lg font-semibold text-blue-900 flex items-center">
                    <i class="material-icons mr-2 text-blue-600">bug_report</i> Log Details
                </h2>
                <button onclick="hideLogModal()" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                    <i class="material-icons">close</i>
                </button>
            </div>

            <!-- Content -->
            <div class="px-6 py-4 max-h-96 sm:max-h-[60vh] overflow-y-auto">
                <div class="mb-4">
                    <span class="inline-block px-3 py-1 text-xs font-semibold rounded-full bg-gray-200 text-gray-800 mb-2">Full Error Message</span>
                    <pre id="log-content" class="bg-gray-100 rounded-lg p-4 text-sm text-red-800 overflow-x-auto whitespace-pre-wrap border border-red-200 shadow-inner max-h-80 overflow-y-auto"></pre>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
@endpush

@push('scripts')
<!-- DataTables JS -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    let table = $('#logs-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '{{ route("admin.logs.index") }}',
            type: 'GET',
            data: function(d) {
                d.month = $('#log-month').val();
            }
        },
        columns: [
            {
                data: 'srno',
                name: 'srno',
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            {
                data: 'date',
                name: 'date',
                orderable: true,
                searchable: true
            },
            {
                data: 'env',
                name: 'env',
                orderable: true,
                searchable: true
            },
            {
                data: 'level',
                name: 'level',
                orderable: true,
                searchable: true
            },
            {
                data: 'message',
                name: 'message',
                orderable: false,
                searchable: true
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                className: 'text-center'
            }
        ],
            order: [[1, 'desc']],
        pageLength: 10,
        lengthMenu: [
            [10, 25, 50, 100],
            [10, 25, 50, 100]
        ],
        language: {
            processing: '<div class="flex items-center justify-center py-4"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div><span class="ml-2">Loading data...</span></div>',
            paginate: {
                previous: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-left"><polyline points="15 18 9 12 15 6"></polyline></svg>',
                next: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-right"><polyline points="9 18 15 12 9 6"></polyline></svg>'
            },
            info: "Showing _START_ to _END_ of _TOTAL_ entries"
        },
        dom: 'rtip', // Show table, processing, info, and pagination
        responsive: true,
        preDrawCallback: function() {
            // Add processing class to wrapper when processing
            if ($('.dataTables_processing').css('display') !== 'none') {
                $('.dataTables_wrapper').addClass('processing');
            }
        },
        drawCallback: function(settings) {
            // Remove processing class when done
            $('.dataTables_wrapper').removeClass('processing');

            // Style pagination buttons
            $('.paginate_button').addClass('px-3 py-2 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700');
            $('.paginate_button.current').addClass('z-10 px-3 py-2 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700').removeClass('text-gray-500 bg-white border-gray-300');
            $('.paginate_button.previous').addClass('ml-0 rounded-l-lg');
            $('.paginate_button.next').addClass('rounded-r-lg');

            // Ensure SVG icons are properly centered
            $('.paginate_button svg').parent().css({
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center'
            });

            // No need to rebind events since HTML is generated in controller
        }
    });
    // Custom search functionality
    $('#search-input').on('keyup', function() {
        table.search($(this).val()).draw();
    });

    // Custom page length functionality
    $('#page-length').on('change', function() {
        table.page.len(parseInt($(this).val())).draw();
    });

    // Refresh table functionality
    $('#refresh-table').on('click', function() {
        table.ajax.reload();
    });

    // Month filter functionality
    $('#log-month').on('change', function() {
        table.ajax.reload();
    });
    // Delete month log functionality with SweetAlert2
    $('#delete-month-log').on('click', function() {
        let month = $('#log-month').val();
        if (!month) {
            Swal.fire({
                title: 'No Month Selected',
                text: 'Please select a month to delete.',
                icon: 'warning',
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'OK'
            });
            return;
        }

        Swal.fire({
            title: 'Are you sure?',
            text: "You are about to delete the log file for " + $('#log-month option:selected').text() + ". This action cannot be undone!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: "{{ route('admin.logs.delete-monthly') }}",
                    type: 'POST',
                    data: {
                        month: month,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(res) {
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'Log file deleted successfully.',
                            icon: 'success',
                            confirmButtonColor: '#3085d6'
                        });
                        table.ajax.reload();
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Error!',
                            text: xhr.responseJSON && xhr.responseJSON.error ? xhr.responseJSON.error : 'Failed to delete log file.',
                            icon: 'error',
                            confirmButtonColor: '#3085d6'
                        });
                    }
                });
            }
        });
    });

    // Custom event handler for processing state
    $(document).ajaxStart(function() {
        $('.dataTables_wrapper').addClass('processing');
    }).ajaxStop(function() {
        $('.dataTables_wrapper').removeClass('processing');
    });
});

// Simple modal functions
function showLogModal(button) {
    console.log('Showing log modal');
    const message = button.getAttribute('data-message');
    const modal = document.getElementById('log-modal');
    const content = document.getElementById('log-content');

    if (modal && content) {
        content.textContent = message;
        modal.classList.remove('hidden');
        console.log('Modal shown successfully');
    } else {
        console.error('Modal or content element not found');
    }
}

function hideLogModal() {
    console.log('Hiding log modal');
    const modal = document.getElementById('log-modal');
    if (modal) {
        modal.classList.add('hidden');
        console.log('Modal hidden successfully');
    } else {
        console.error('Modal not found');
    }
}

// Close modal on ESC key
$(document).on('keydown', function(e) {
    if (e.key === 'Escape') {
        hideLogModal();
    }
});

// Close modal when clicking outside
$(document).on('click', '#log-modal', function(e) {
    if (e.target === this) {
        hideLogModal();
    }
});
</script>
@endpush