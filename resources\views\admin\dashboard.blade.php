@extends('layouts.admin')

@section('title', 'Dashboard')

@php
$pageTitle = 'Dashboard';
$pageDescription = 'Welcome to your admin dashboard';
$breadcrumbs = [
['title' => 'Dashboard', 'url' => '#']
];
@endphp

@section('content')
<!-- Key Metrics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Users -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-blue-600">people</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Users</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_users']) }}</p>
            </div>
        </div>
    </div>

    <!-- Total News -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-green-600">article</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total News</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_news']) }}</p>
            </div>
        </div>
    </div>

    <!-- Pending News -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-orange-600">hourglass_empty</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Pending News</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['pending_news']) }}</p>
                @if($stats['pending_news'] > 0)
                    <p class="text-sm text-orange-600">Needs attention</p>
                @else
                    <p class="text-sm text-green-600">All caught up!</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Published News -->
    <div class="material-card p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="material-icons text-purple-600">check_circle</i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Published News</p>
                <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['published_news']) }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Tables Row -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Recent Activity -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="material-icons text-blue-600 text-sm">person_add</i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">New user registered</p>
                        <p class="text-xs text-gray-500">2 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="material-icons text-green-600 text-sm">shopping_cart</i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">New order received</p>
                        <p class="text-xs text-gray-500">5 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="material-icons text-orange-600 text-sm">payment</i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">Payment processed</p>
                        <p class="text-xs text-gray-500">10 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="material-icons text-purple-600 text-sm">feedback</i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">New review submitted</p>
                        <p class="text-xs text-gray-500">15 minutes ago</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-2 gap-4">
                <a href="{{ route('admin.users.create') }}" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="material-icons text-blue-600 mb-2">person_add</i>
                    <span class="text-sm font-medium text-gray-900">Add User</span>
                </a>

                <a href="{{ route('admin.news.create') }}" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="material-icons text-green-600 mb-2">add</i>
                    <span class="text-sm font-medium text-gray-900">Create News</span>
                </a>

                <a href="{{ route('admin.pending-news.index') }}" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="material-icons text-orange-600 mb-2">pending_actions</i>
                    <span class="text-sm font-medium text-gray-900">Review Pending</span>
                </a>

                <a href="{{ route('admin.news-integration.index') }}" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="material-icons text-purple-600 mb-2">sync</i>
                    <span class="text-sm font-medium text-gray-900">News Integration</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Users Table -->
<div class="material-card">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Recent Users</h3>
            <a href="{{ route('admin.users.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View all</a>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($recent_users as $user)
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8">
                                <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                    <span class="text-sm font-medium text-blue-600">{{ substr($user->name, 0, 1) }}</span>
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $user->email }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $user->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ ucfirst($user->status) }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $user->created_at->format('M d, Y') }}</td>
                </tr>
                @empty
                <tr>
                    <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No users found</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
@endsection