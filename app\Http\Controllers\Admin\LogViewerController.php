<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Yajra\DataTables\Facades\DataTables;

class LogViewerController extends Controller
{
    public function index(Request $request)
    {
        // Check if this is an AJAX request for datatable
        if ($request->ajax()) {
            $month = $request->input('month');
            $logs = $this->getLogsData($month);

            return DataTables::collection(collect($logs))
                ->addColumn('srno', function ($log) {
                    return '';
                })
                ->editColumn('date', function ($log) {
                    return $log['date'];
                })
                ->editColumn('env', function ($log) {
                    return $log['env'];
                })
                ->editColumn('level', function ($log) {
                    $levelColors = [
                        'ERROR' => 'bg-red-100 text-red-800',
                        'WARNING' => 'bg-yellow-100 text-yellow-800',
                        'INFO' => 'bg-blue-100 text-blue-800',
                        'DEBUG' => 'bg-gray-100 text-gray-800',
                    ];
                    $colorClass = $levelColors[$log['level']] ?? 'bg-gray-100 text-gray-800';
                    return '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ' . $colorClass . '">' . $log['level'] . '</span>';
                })
                ->editColumn('message', function ($log) {
                    if (!$log['message']) return '';
                    $firstLine = explode("\n", $log['message'])[0];
                    $hasMore = strpos($log['message'], "\n") !== false;
                    $moreText = $hasMore ? ' <span class="text-xs text-blue-500">[more]</span>' : '';
                    return '<span class="truncate inline-block max-w-xs align-middle" title="' . htmlspecialchars($firstLine) . '">' . htmlspecialchars($firstLine) . $moreText . '</span>';
                })
                ->addColumn('actions', function ($log) {
                    $safeMessage = htmlspecialchars($log['message']);

                    return '
                        <div class="flex items-center justify-center">
                            <button class="view-log-btn action-btn view"
                                    title="View Full Log"
                                    data-message="' . $safeMessage . '"
                                    onclick="showLogModal(this)">
                                <i class="material-icons text-sm">visibility</i>
                            </button>
                        </div>
                    ';
                })
                ->rawColumns(['level', 'message', 'actions'])
                ->make(true);
        }

        // Collect log files and generate month options
        $logFiles = collect(File::files(storage_path('logs')))
            ->map(function($file) { return $file->getFilename(); })
            ->filter(function($name) { return preg_match('/^laravel(-\d{2}-\d{4})?\.log$/', $name); })
            ->sortDesc()
            ->values();
        $monthOptions = $logFiles->map(function($name) {
            if ($name === 'laravel.log') {
                $month = now()->format('m-Y');
                $label = Carbon::createFromFormat('m-Y', $month)->format('F Y') . ' (Current)';
                return ['value' => $month, 'label' => $label];
            }
            if (preg_match('/laravel-(\d{2})-(\d{4})\.log/', $name, $m)) {
                $month = $m[1] . '-' . $m[2];
                $label = Carbon::createFromFormat('m-Y', $month)->format('F Y');
                return ['value' => $month, 'label' => $label];
            }
            return null;
        })->filter()->unique('value')->values();
        $currentMonth = now()->format('m-Y');
        $pageTitle = 'Laravel Log Viewer';
        $pageDescription = 'Browse application logs in a modern table.';
        $breadcrumbs = [
            ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
            ['title' => 'Log Viewer', 'url' => '#']
        ];
        return view('admin.logs.index', compact('monthOptions', 'currentMonth', 'pageTitle', 'pageDescription', 'breadcrumbs'));
    }

    /**
     * Get logs data for the specified month
     */
    private function getLogsData($month = null)
    {
        $logPath = storage_path('logs/laravel.log');
        if ($month) {
            $logPath = storage_path('logs/laravel-' . $month . '.log');
        }

        $logs = [];

        if (File::exists($logPath)) {
            $lines = file($logPath, FILE_IGNORE_NEW_LINES);
            $entry = '';

            foreach ($lines as $line) {
                if (preg_match('/^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]/', $line)) {
                    // New log entry starts
                    if (!empty($entry)) {
                        $logs[] = $this->parseLogEntry($entry);
                    }
                    $entry = $line;
                } else {
                    // Append to previous entry
                    $entry .= "\n" . $line;
                }
            }

            // Don't forget the last entry
            if (!empty($entry)) {
                $logs[] = $this->parseLogEntry($entry);
            }
        }

        return $logs;
    }
    private function parseLogEntry($entry)
    {
        if (preg_match('/^\[(.*?)\] (\w+)\.(\w+): (.*)$/s', $entry, $matches)) {
            return [
                'date' => $matches[1],
                'env' => $matches[2],
                'level' => $matches[3],
                'message' => $matches[4],
            ];
    }

    return [
        'date' => '',
        'env' => '',
        'level' => '',
        'message' => $entry, // fallback in case it doesn’t match
    ];
}

    public function deleteMonthlyLog(Request $request)
    {
        $month = $request->input('month');
        if (!$month) {
            return response()->json(['error' => 'Month is required.'], 400);
        }
        $logPath = storage_path('logs/laravel-' . $month . '.log');
        if (!File::exists($logPath)) {
            return response()->json(['error' => 'Log file not found.'], 404);
        }
        File::delete($logPath);
        return response()->json(['success' => true]);
    }

}
