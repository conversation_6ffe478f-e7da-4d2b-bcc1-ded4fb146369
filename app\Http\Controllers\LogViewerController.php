<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class LogViewerController extends Controller
{
    public function index()
    {
        // Collect log files and generate month options
        $logFiles = collect(File::files(storage_path('logs')))
            ->map(function($file) { return $file->getFilename(); })
            ->filter(function($name) { return preg_match('/^laravel(-\d{2}-\d{4})?\.log$/', $name); })
            ->sortDesc()
            ->values();
        $monthOptions = $logFiles->map(function($name) {
            if ($name === 'laravel.log') {
                $month = now()->format('m-Y');
                $label = Carbon::createFromFormat('m-Y', $month)->format('F Y') . ' (Current)';
                return ['value' => $month, 'label' => $label];
            }
            if (preg_match('/laravel-(\d{2})-(\d{4})\.log/', $name, $m)) {
                $month = $m[1] . '-' . $m[2];
                $label = Carbon::createFromFormat('m-Y', $month)->format('F Y');
                return ['value' => $month, 'label' => $label];
            }
            return null;
        })->filter()->unique('value')->values();
        $currentMonth = now()->format('m-Y');
        $pageTitle = 'Laravel Log Viewer';
        $pageDescription = 'Browse application logs in a modern table.';
        $breadcrumbs = [
            ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
            ['title' => 'Log Viewer', 'url' => '#']
        ];
        return view('admin.logs.index', compact('monthOptions', 'currentMonth', 'pageTitle', 'pageDescription', 'breadcrumbs'));
    }

    public function fetch(Request $request)
    {
        $month = $request->input('month');
        $logPath = storage_path('logs/laravel.log');
        if ($month) {
            $logPath = storage_path('logs/laravel-' . $month . '.log');
        }
        $logs = [];
        if (File::exists($logPath)) {
            $lines = array_reverse(file($logPath, FILE_IGNORE_NEW_LINES));
            foreach ($lines as $line) {
                if (preg_match('/^\[(.*?)\] (\w+)\.(\w+): (.*)$/', $line, $matches)) {
                    $logs[] = [
                        'date' => $matches[1],
                        'env' => $matches[2],
                        'level' => $matches[3],
                        'message' => $matches[4],
                    ];
                }
            }
        }

        // DataTables server-side params
        $draw = intval($request->input('draw'));
        $start = intval($request->input('start', 0));
        $length = intval($request->input('length', 10));
        $search = $request->input('search.value');

        $filtered = $logs;
        if ($search) {
            $filtered = array_filter($logs, function($row) use ($search) {
                return stripos($row['date'], $search) !== false
                    || stripos($row['env'], $search) !== false
                    || stripos($row['level'], $search) !== false
                    || stripos($row['message'], $search) !== false;
            });
        }
        $recordsFiltered = count($filtered);
        $recordsTotal = count($logs);
        $data = array_slice(array_values($filtered), $start, $length);

        return response()->json([
            'draw' => $draw,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered,
            'data' => $data,
        ]);
    }

    public function deleteMonthlyLog(Request $request)
    {
        $month = $request->input('month');
        if (!$month) {
            return response()->json(['error' => 'Month is required.'], 400);
        }
        $logPath = storage_path('logs/laravel-' . $month . '.log');
        if (!File::exists($logPath)) {
            return response()->json(['error' => 'Log file not found.'], 404);
        }
        File::delete($logPath);
        return response()->json(['success' => true]);
    }

}
