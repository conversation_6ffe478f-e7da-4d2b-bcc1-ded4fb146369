<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\News;
use App\Models\LatestNewsTitle;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        // Get key statistics for the dashboard
        $stats = [
            // User Management
            'total_users' => User::count(),

            // News Management
            'total_news' => News::count(),
            'pending_news' => News::where('approval_status', 'pending')->count(),
            'published_news' => News::published()->count(),
        ];

        // Get recent users
        $recent_users = User::latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recent_users'));
    }
}
